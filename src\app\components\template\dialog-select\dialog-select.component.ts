import { Component, OnInit,  } from '@angular/core';

@Component({
  selector: 'app-dialog-select',
  templateUrl: './dialog-select.component.html',
  styleUrls: ['./dialog-select.component.css']
})
export class DialogSelectComponent implements OnInit {
  selected: any[] = [];

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: { 
    items: any[], 
    title?: string, 
    displayField?: string 
  }) {}

  ngOnInit(): void {
  }

}
